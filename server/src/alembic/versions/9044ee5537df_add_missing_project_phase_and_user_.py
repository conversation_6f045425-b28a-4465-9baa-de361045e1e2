"""add_missing_project_phase_and_user_basic_preferences_tables

Revision ID: 9044ee5537df
Revises: 8e92157c3f50
Create Date: 2025-08-08 14:37:16.299037

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from src.core.models.base import EnumType

# revision identifiers, used by Alembic.
revision = "9044ee5537df"
down_revision = "8e92157c3f50"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "project_templates",
        sa.Column(
            "template_id", sa.UUID(as_uuid=False), nullable=False, comment="Unique UUID identifier for the template"
        ),
        sa.Column(
            "template_type",
            EnumType(),
            nullable=False,
            comment="Type of electrical project template",
        ),
        sa.Column(
            "category",
            sa.String(length=100),
            nullable=False,
            comment="Template category (industrial, commercial, residential)",
        ),
        sa.Column("description", sa.Text(), nullable=True, comment="Template description"),
        sa.Column("phases_config", sa.JSON(), nullable=True, comment="JSON configuration of default phases"),
        sa.Column("milestones_config", sa.JSON(), nullable=True, comment="JSON configuration of default milestones"),
        sa.Column("default_settings", sa.JSON(), nullable=True, comment="JSON of default project settings"),
        sa.Column(
            "compliance_standards",
            sa.JSON(),
            nullable=True,
            comment="JSON array of applicable standards (IEC, IEEE, EN)",
        ),
        sa.Column("is_public", sa.Boolean(), nullable=False, comment="Whether template is available to all users"),
        sa.Column("created_by_user_id", sa.Integer(), nullable=True, comment="User who created the template"),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "created_by_user_id", name="uq_user_template_name"),
        sa.UniqueConstraint("template_id", name="uq_project_template_id"),
    )
    op.create_index(op.f("ix_project_templates_category"), "project_templates", ["category"], unique=False)
    op.create_index(
        op.f("ix_project_templates_created_by_user_id"), "project_templates", ["created_by_user_id"], unique=False
    )
    op.create_index(op.f("ix_project_templates_is_public"), "project_templates", ["is_public"], unique=False)
    op.create_index(op.f("ix_project_templates_template_id"), "project_templates", ["template_id"], unique=True)
    op.create_index(op.f("ix_project_templates_template_type"), "project_templates", ["template_type"], unique=False)
    op.create_table(
        "user_basic_preferences",
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("ui_theme", sa.String(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id"),
    )
    op.create_table(
        "project_phases",
        sa.Column("phase_id", sa.UUID(as_uuid=False), nullable=False, comment="Unique UUID identifier for the phase"),
        sa.Column("project_id", sa.Integer(), nullable=False, comment="Foreign key to the associated project"),
        sa.Column("phase_type", EnumType(), nullable=False, comment="Type of project phase"),
        sa.Column("start_date", sa.DateTime(), nullable=False, comment="Phase start date"),
        sa.Column("end_date", sa.DateTime(), nullable=True, comment="Phase end date"),
        sa.Column("progress_percentage", sa.Integer(), nullable=False, comment="Current completion percentage (0-100)"),
        sa.Column("is_active", sa.Boolean(), nullable=False, comment="Whether this phase is currently active"),
        sa.Column("prerequisites", sa.JSON(), nullable=True, comment="JSON array of prerequisite phase IDs"),
        sa.Column("deliverables", sa.JSON(), nullable=True, comment="JSON array of expected deliverables"),
        sa.Column("notes", sa.Text(), nullable=True, comment="Phase notes and comments"),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("phase_id", name="uq_project_phase_id"),
        sa.UniqueConstraint("project_id", "phase_type", name="uq_project_phase_type"),
    )
    op.create_index(op.f("ix_project_phases_end_date"), "project_phases", ["end_date"], unique=False)
    op.create_index(op.f("ix_project_phases_is_active"), "project_phases", ["is_active"], unique=False)
    op.create_index(op.f("ix_project_phases_phase_id"), "project_phases", ["phase_id"], unique=True)
    op.create_index(op.f("ix_project_phases_phase_type"), "project_phases", ["phase_type"], unique=False)
    op.create_index(op.f("ix_project_phases_project_id"), "project_phases", ["project_id"], unique=False)
    op.create_index(op.f("ix_project_phases_start_date"), "project_phases", ["start_date"], unique=False)
    op.create_table(
        "project_milestones",
        sa.Column(
            "milestone_id", sa.UUID(as_uuid=False), nullable=False, comment="Unique UUID identifier for the milestone"
        ),
        sa.Column("phase_id", sa.Integer(), nullable=False, comment="Foreign key to the associated project phase"),
        sa.Column("title", sa.String(length=255), nullable=False, comment="Milestone title"),
        sa.Column("description", sa.Text(), nullable=True, comment="Detailed milestone description"),
        sa.Column("due_date", sa.DateTime(), nullable=False, comment="Milestone due date"),
        sa.Column("completion_date", sa.DateTime(), nullable=True, comment="Actual completion date"),
        sa.Column("status", EnumType(), nullable=False, comment="Milestone status"),
        sa.Column("assigned_user_id", sa.Integer(), nullable=True, comment="User responsible for milestone completion"),
        sa.Column("dependencies", sa.JSON(), nullable=True, comment="JSON array of dependent milestone IDs"),
        sa.Column("completion_criteria", sa.JSON(), nullable=True, comment="JSON array of completion criteria"),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["assigned_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["phase_id"],
            ["project_phases.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("milestone_id", name="uq_project_milestone_id"),
    )
    op.create_index(
        op.f("ix_project_milestones_assigned_user_id"), "project_milestones", ["assigned_user_id"], unique=False
    )
    op.create_index(
        op.f("ix_project_milestones_completion_date"), "project_milestones", ["completion_date"], unique=False
    )
    op.create_index(op.f("ix_project_milestones_due_date"), "project_milestones", ["due_date"], unique=False)
    op.create_index(op.f("ix_project_milestones_milestone_id"), "project_milestones", ["milestone_id"], unique=True)
    op.create_index(op.f("ix_project_milestones_phase_id"), "project_milestones", ["phase_id"], unique=False)
    op.create_index(op.f("ix_project_milestones_status"), "project_milestones", ["status"], unique=False)
    op.drop_table("user_preferences")
    op.add_column("tasks", sa.Column("name", sa.String(length=255), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tasks", "name")
    op.create_table(
        "user_preferences",
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("ui_theme", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("name", sa.VARCHAR(length=255), autoincrement=False, nullable=False),
        sa.Column("notes", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("updated_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("is_deleted", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column("deleted_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column("deleted_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"], ["users.id"], name=op.f("UserPreference_deleted_by_user_id_fkey")
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], name=op.f("UserPreference_user_id_fkey")),
        sa.PrimaryKeyConstraint("id", name=op.f("UserPreference_pkey")),
        sa.UniqueConstraint(
            "user_id",
            name=op.f("UserPreference_user_id_key"),
            postgresql_include=[],
            postgresql_nulls_not_distinct=False,
        ),
    )
    op.drop_index(op.f("ix_project_milestones_status"), table_name="project_milestones")
    op.drop_index(op.f("ix_project_milestones_phase_id"), table_name="project_milestones")
    op.drop_index(op.f("ix_project_milestones_milestone_id"), table_name="project_milestones")
    op.drop_index(op.f("ix_project_milestones_due_date"), table_name="project_milestones")
    op.drop_index(op.f("ix_project_milestones_completion_date"), table_name="project_milestones")
    op.drop_index(op.f("ix_project_milestones_assigned_user_id"), table_name="project_milestones")
    op.drop_table("project_milestones")
    op.drop_index(op.f("ix_project_phases_start_date"), table_name="project_phases")
    op.drop_index(op.f("ix_project_phases_project_id"), table_name="project_phases")
    op.drop_index(op.f("ix_project_phases_phase_type"), table_name="project_phases")
    op.drop_index(op.f("ix_project_phases_phase_id"), table_name="project_phases")
    op.drop_index(op.f("ix_project_phases_is_active"), table_name="project_phases")
    op.drop_index(op.f("ix_project_phases_end_date"), table_name="project_phases")
    op.drop_table("project_phases")
    op.drop_table("user_basic_preferences")
    op.drop_index(op.f("ix_project_templates_template_type"), table_name="project_templates")
    op.drop_index(op.f("ix_project_templates_template_id"), table_name="project_templates")
    op.drop_index(op.f("ix_project_templates_is_public"), table_name="project_templates")
    op.drop_index(op.f("ix_project_templates_created_by_user_id"), table_name="project_templates")
    op.drop_index(op.f("ix_project_templates_category"), table_name="project_templates")
    op.drop_table("project_templates")
    # ### end Alembic commands ###
